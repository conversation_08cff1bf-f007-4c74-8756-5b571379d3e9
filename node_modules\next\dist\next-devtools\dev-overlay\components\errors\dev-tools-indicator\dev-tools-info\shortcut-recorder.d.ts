export declare function ShortcutRecorder({ value, onChange, }: {
    value: string[] | null;
    onChange: (value: string | null) => void;
}): import("react/jsx-runtime").JSX.Element;
export declare const SHORTCUT_RECORDER_STYLES: string;
export declare function isMac(): boolean | undefined;
export declare function isIPhone(): boolean | undefined;
export declare function isIPad(): boolean | undefined;
export declare function isIOS(): boolean | undefined;
export declare function isApple(): boolean | undefined;
