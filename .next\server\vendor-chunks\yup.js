"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/yup";
exports.ids = ["vendor-chunks/yup"];
exports.modules = {

/***/ "(ssr)/./node_modules/yup/index.esm.js":
/*!***************************************!*\
  !*** ./node_modules/yup/index.esm.js ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ArraySchema: () => (/* binding */ ArraySchema),\n/* harmony export */   BooleanSchema: () => (/* binding */ BooleanSchema),\n/* harmony export */   DateSchema: () => (/* binding */ DateSchema),\n/* harmony export */   LazySchema: () => (/* binding */ Lazy),\n/* harmony export */   MixedSchema: () => (/* binding */ MixedSchema),\n/* harmony export */   NumberSchema: () => (/* binding */ NumberSchema),\n/* harmony export */   ObjectSchema: () => (/* binding */ ObjectSchema),\n/* harmony export */   Schema: () => (/* binding */ Schema),\n/* harmony export */   StringSchema: () => (/* binding */ StringSchema),\n/* harmony export */   TupleSchema: () => (/* binding */ TupleSchema),\n/* harmony export */   ValidationError: () => (/* binding */ ValidationError),\n/* harmony export */   addMethod: () => (/* binding */ addMethod),\n/* harmony export */   array: () => (/* binding */ create$2),\n/* harmony export */   bool: () => (/* binding */ create$7),\n/* harmony export */   boolean: () => (/* binding */ create$7),\n/* harmony export */   date: () => (/* binding */ create$4),\n/* harmony export */   defaultLocale: () => (/* binding */ locale),\n/* harmony export */   getIn: () => (/* binding */ getIn),\n/* harmony export */   isSchema: () => (/* binding */ isSchema),\n/* harmony export */   lazy: () => (/* binding */ create),\n/* harmony export */   mixed: () => (/* binding */ create$8),\n/* harmony export */   number: () => (/* binding */ create$5),\n/* harmony export */   object: () => (/* binding */ create$3),\n/* harmony export */   printValue: () => (/* binding */ printValue),\n/* harmony export */   reach: () => (/* binding */ reach),\n/* harmony export */   ref: () => (/* binding */ create$9),\n/* harmony export */   setLocale: () => (/* binding */ setLocale),\n/* harmony export */   string: () => (/* binding */ create$6),\n/* harmony export */   tuple: () => (/* binding */ create$1)\n/* harmony export */ });\n/* harmony import */ var property_expr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! property-expr */ \"(ssr)/./node_modules/property-expr/index.js\");\n/* harmony import */ var property_expr__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(property_expr__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var tiny_case__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tiny-case */ \"(ssr)/./node_modules/tiny-case/index.js\");\n/* harmony import */ var tiny_case__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(tiny_case__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var toposort__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! toposort */ \"(ssr)/./node_modules/toposort/index.js\");\n/* harmony import */ var toposort__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(toposort__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\n\nconst toString = Object.prototype.toString;\nconst errorToString = Error.prototype.toString;\nconst regExpToString = RegExp.prototype.toString;\nconst symbolToString = typeof Symbol !== 'undefined' ? Symbol.prototype.toString : () => '';\nconst SYMBOL_REGEXP = /^Symbol\\((.*)\\)(.*)$/;\nfunction printNumber(val) {\n  if (val != +val) return 'NaN';\n  const isNegativeZero = val === 0 && 1 / val < 0;\n  return isNegativeZero ? '-0' : '' + val;\n}\nfunction printSimpleValue(val, quoteStrings = false) {\n  if (val == null || val === true || val === false) return '' + val;\n  const typeOf = typeof val;\n  if (typeOf === 'number') return printNumber(val);\n  if (typeOf === 'string') return quoteStrings ? `\"${val}\"` : val;\n  if (typeOf === 'function') return '[Function ' + (val.name || 'anonymous') + ']';\n  if (typeOf === 'symbol') return symbolToString.call(val).replace(SYMBOL_REGEXP, 'Symbol($1)');\n  const tag = toString.call(val).slice(8, -1);\n  if (tag === 'Date') return isNaN(val.getTime()) ? '' + val : val.toISOString(val);\n  if (tag === 'Error' || val instanceof Error) return '[' + errorToString.call(val) + ']';\n  if (tag === 'RegExp') return regExpToString.call(val);\n  return null;\n}\nfunction printValue(value, quoteStrings) {\n  let result = printSimpleValue(value, quoteStrings);\n  if (result !== null) return result;\n  return JSON.stringify(value, function (key, value) {\n    let result = printSimpleValue(this[key], quoteStrings);\n    if (result !== null) return result;\n    return value;\n  }, 2);\n}\n\nfunction toArray(value) {\n  return value == null ? [] : [].concat(value);\n}\n\nlet _Symbol$toStringTag, _Symbol$hasInstance, _Symbol$toStringTag2;\nlet strReg = /\\$\\{\\s*(\\w+)\\s*\\}/g;\n_Symbol$toStringTag = Symbol.toStringTag;\nclass ValidationErrorNoStack {\n  constructor(errorOrErrors, value, field, type) {\n    this.name = void 0;\n    this.message = void 0;\n    this.value = void 0;\n    this.path = void 0;\n    this.type = void 0;\n    this.params = void 0;\n    this.errors = void 0;\n    this.inner = void 0;\n    this[_Symbol$toStringTag] = 'Error';\n    this.name = 'ValidationError';\n    this.value = value;\n    this.path = field;\n    this.type = type;\n    this.errors = [];\n    this.inner = [];\n    toArray(errorOrErrors).forEach(err => {\n      if (ValidationError.isError(err)) {\n        this.errors.push(...err.errors);\n        const innerErrors = err.inner.length ? err.inner : [err];\n        this.inner.push(...innerErrors);\n      } else {\n        this.errors.push(err);\n      }\n    });\n    this.message = this.errors.length > 1 ? `${this.errors.length} errors occurred` : this.errors[0];\n  }\n}\n_Symbol$hasInstance = Symbol.hasInstance;\n_Symbol$toStringTag2 = Symbol.toStringTag;\nclass ValidationError extends Error {\n  static formatError(message, params) {\n    // Attempt to make the path more friendly for error message interpolation.\n    const path = params.label || params.path || 'this';\n    // Store the original path under `originalPath` so it isn't lost to custom\n    // message functions; e.g., ones provided in `setLocale()` calls.\n    params = Object.assign({}, params, {\n      path,\n      originalPath: params.path\n    });\n    if (typeof message === 'string') return message.replace(strReg, (_, key) => printValue(params[key]));\n    if (typeof message === 'function') return message(params);\n    return message;\n  }\n  static isError(err) {\n    return err && err.name === 'ValidationError';\n  }\n  constructor(errorOrErrors, value, field, type, disableStack) {\n    const errorNoStack = new ValidationErrorNoStack(errorOrErrors, value, field, type);\n    if (disableStack) {\n      return errorNoStack;\n    }\n    super();\n    this.value = void 0;\n    this.path = void 0;\n    this.type = void 0;\n    this.params = void 0;\n    this.errors = [];\n    this.inner = [];\n    this[_Symbol$toStringTag2] = 'Error';\n    this.name = errorNoStack.name;\n    this.message = errorNoStack.message;\n    this.type = errorNoStack.type;\n    this.value = errorNoStack.value;\n    this.path = errorNoStack.path;\n    this.errors = errorNoStack.errors;\n    this.inner = errorNoStack.inner;\n    if (Error.captureStackTrace) {\n      Error.captureStackTrace(this, ValidationError);\n    }\n  }\n  static [_Symbol$hasInstance](inst) {\n    return ValidationErrorNoStack[Symbol.hasInstance](inst) || super[Symbol.hasInstance](inst);\n  }\n}\n\nlet mixed = {\n  default: '${path} is invalid',\n  required: '${path} is a required field',\n  defined: '${path} must be defined',\n  notNull: '${path} cannot be null',\n  oneOf: '${path} must be one of the following values: ${values}',\n  notOneOf: '${path} must not be one of the following values: ${values}',\n  notType: ({\n    path,\n    type,\n    value,\n    originalValue\n  }) => {\n    const castMsg = originalValue != null && originalValue !== value ? ` (cast from the value \\`${printValue(originalValue, true)}\\`).` : '.';\n    return type !== 'mixed' ? `${path} must be a \\`${type}\\` type, ` + `but the final value was: \\`${printValue(value, true)}\\`` + castMsg : `${path} must match the configured type. ` + `The validated value was: \\`${printValue(value, true)}\\`` + castMsg;\n  }\n};\nlet string = {\n  length: '${path} must be exactly ${length} characters',\n  min: '${path} must be at least ${min} characters',\n  max: '${path} must be at most ${max} characters',\n  matches: '${path} must match the following: \"${regex}\"',\n  email: '${path} must be a valid email',\n  url: '${path} must be a valid URL',\n  uuid: '${path} must be a valid UUID',\n  datetime: '${path} must be a valid ISO date-time',\n  datetime_precision: '${path} must be a valid ISO date-time with a sub-second precision of exactly ${precision} digits',\n  datetime_offset: '${path} must be a valid ISO date-time with UTC \"Z\" timezone',\n  trim: '${path} must be a trimmed string',\n  lowercase: '${path} must be a lowercase string',\n  uppercase: '${path} must be a upper case string'\n};\nlet number = {\n  min: '${path} must be greater than or equal to ${min}',\n  max: '${path} must be less than or equal to ${max}',\n  lessThan: '${path} must be less than ${less}',\n  moreThan: '${path} must be greater than ${more}',\n  positive: '${path} must be a positive number',\n  negative: '${path} must be a negative number',\n  integer: '${path} must be an integer'\n};\nlet date = {\n  min: '${path} field must be later than ${min}',\n  max: '${path} field must be at earlier than ${max}'\n};\nlet boolean = {\n  isValue: '${path} field must be ${value}'\n};\nlet object = {\n  noUnknown: '${path} field has unspecified keys: ${unknown}',\n  exact: '${path} object contains unknown properties: ${properties}'\n};\nlet array = {\n  min: '${path} field must have at least ${min} items',\n  max: '${path} field must have less than or equal to ${max} items',\n  length: '${path} must have ${length} items'\n};\nlet tuple = {\n  notType: params => {\n    const {\n      path,\n      value,\n      spec\n    } = params;\n    const typeLen = spec.types.length;\n    if (Array.isArray(value)) {\n      if (value.length < typeLen) return `${path} tuple value has too few items, expected a length of ${typeLen} but got ${value.length} for value: \\`${printValue(value, true)}\\``;\n      if (value.length > typeLen) return `${path} tuple value has too many items, expected a length of ${typeLen} but got ${value.length} for value: \\`${printValue(value, true)}\\``;\n    }\n    return ValidationError.formatError(mixed.notType, params);\n  }\n};\nvar locale = Object.assign(Object.create(null), {\n  mixed,\n  string,\n  number,\n  date,\n  object,\n  array,\n  boolean,\n  tuple\n});\n\nconst isSchema = obj => obj && obj.__isYupSchema__;\n\nclass Condition {\n  static fromOptions(refs, config) {\n    if (!config.then && !config.otherwise) throw new TypeError('either `then:` or `otherwise:` is required for `when()` conditions');\n    let {\n      is,\n      then,\n      otherwise\n    } = config;\n    let check = typeof is === 'function' ? is : (...values) => values.every(value => value === is);\n    return new Condition(refs, (values, schema) => {\n      var _branch;\n      let branch = check(...values) ? then : otherwise;\n      return (_branch = branch == null ? void 0 : branch(schema)) != null ? _branch : schema;\n    });\n  }\n  constructor(refs, builder) {\n    this.fn = void 0;\n    this.refs = refs;\n    this.refs = refs;\n    this.fn = builder;\n  }\n  resolve(base, options) {\n    let values = this.refs.map(ref =>\n    // TODO: ? operator here?\n    ref.getValue(options == null ? void 0 : options.value, options == null ? void 0 : options.parent, options == null ? void 0 : options.context));\n    let schema = this.fn(values, base, options);\n    if (schema === undefined ||\n    // @ts-ignore this can be base\n    schema === base) {\n      return base;\n    }\n    if (!isSchema(schema)) throw new TypeError('conditions must return a schema object');\n    return schema.resolve(options);\n  }\n}\n\nconst prefixes = {\n  context: '$',\n  value: '.'\n};\nfunction create$9(key, options) {\n  return new Reference(key, options);\n}\nclass Reference {\n  constructor(key, options = {}) {\n    this.key = void 0;\n    this.isContext = void 0;\n    this.isValue = void 0;\n    this.isSibling = void 0;\n    this.path = void 0;\n    this.getter = void 0;\n    this.map = void 0;\n    if (typeof key !== 'string') throw new TypeError('ref must be a string, got: ' + key);\n    this.key = key.trim();\n    if (key === '') throw new TypeError('ref must be a non-empty string');\n    this.isContext = this.key[0] === prefixes.context;\n    this.isValue = this.key[0] === prefixes.value;\n    this.isSibling = !this.isContext && !this.isValue;\n    let prefix = this.isContext ? prefixes.context : this.isValue ? prefixes.value : '';\n    this.path = this.key.slice(prefix.length);\n    this.getter = this.path && (0,property_expr__WEBPACK_IMPORTED_MODULE_0__.getter)(this.path, true);\n    this.map = options.map;\n  }\n  getValue(value, parent, context) {\n    let result = this.isContext ? context : this.isValue ? value : parent;\n    if (this.getter) result = this.getter(result || {});\n    if (this.map) result = this.map(result);\n    return result;\n  }\n\n  /**\n   *\n   * @param {*} value\n   * @param {Object} options\n   * @param {Object=} options.context\n   * @param {Object=} options.parent\n   */\n  cast(value, options) {\n    return this.getValue(value, options == null ? void 0 : options.parent, options == null ? void 0 : options.context);\n  }\n  resolve() {\n    return this;\n  }\n  describe() {\n    return {\n      type: 'ref',\n      key: this.key\n    };\n  }\n  toString() {\n    return `Ref(${this.key})`;\n  }\n  static isRef(value) {\n    return value && value.__isYupRef;\n  }\n}\n\n// @ts-ignore\nReference.prototype.__isYupRef = true;\n\nconst isAbsent = value => value == null;\n\nfunction createValidation(config) {\n  function validate({\n    value,\n    path = '',\n    options,\n    originalValue,\n    schema\n  }, panic, next) {\n    const {\n      name,\n      test,\n      params,\n      message,\n      skipAbsent\n    } = config;\n    let {\n      parent,\n      context,\n      abortEarly = schema.spec.abortEarly,\n      disableStackTrace = schema.spec.disableStackTrace\n    } = options;\n    const resolveOptions = {\n      value,\n      parent,\n      context\n    };\n    function createError(overrides = {}) {\n      const nextParams = resolveParams(Object.assign({\n        value,\n        originalValue,\n        label: schema.spec.label,\n        path: overrides.path || path,\n        spec: schema.spec,\n        disableStackTrace: overrides.disableStackTrace || disableStackTrace\n      }, params, overrides.params), resolveOptions);\n      const error = new ValidationError(ValidationError.formatError(overrides.message || message, nextParams), value, nextParams.path, overrides.type || name, nextParams.disableStackTrace);\n      error.params = nextParams;\n      return error;\n    }\n    const invalid = abortEarly ? panic : next;\n    let ctx = {\n      path,\n      parent,\n      type: name,\n      from: options.from,\n      createError,\n      resolve(item) {\n        return resolveMaybeRef(item, resolveOptions);\n      },\n      options,\n      originalValue,\n      schema\n    };\n    const handleResult = validOrError => {\n      if (ValidationError.isError(validOrError)) invalid(validOrError);else if (!validOrError) invalid(createError());else next(null);\n    };\n    const handleError = err => {\n      if (ValidationError.isError(err)) invalid(err);else panic(err);\n    };\n    const shouldSkip = skipAbsent && isAbsent(value);\n    if (shouldSkip) {\n      return handleResult(true);\n    }\n    let result;\n    try {\n      var _result;\n      result = test.call(ctx, value, ctx);\n      if (typeof ((_result = result) == null ? void 0 : _result.then) === 'function') {\n        if (options.sync) {\n          throw new Error(`Validation test of type: \"${ctx.type}\" returned a Promise during a synchronous validate. ` + `This test will finish after the validate call has returned`);\n        }\n        return Promise.resolve(result).then(handleResult, handleError);\n      }\n    } catch (err) {\n      handleError(err);\n      return;\n    }\n    handleResult(result);\n  }\n  validate.OPTIONS = config;\n  return validate;\n}\n\n// Warning: mutates the input\nfunction resolveParams(params, options) {\n  if (!params) return params;\n  for (const key of Object.keys(params)) {\n    params[key] = resolveMaybeRef(params[key], options);\n  }\n  return params;\n}\nfunction resolveMaybeRef(item, options) {\n  return Reference.isRef(item) ? item.getValue(options.value, options.parent, options.context) : item;\n}\n\nfunction getIn(schema, path, value, context = value) {\n  let parent, lastPart, lastPartDebug;\n\n  // root path: ''\n  if (!path) return {\n    parent,\n    parentPath: path,\n    schema\n  };\n  (0,property_expr__WEBPACK_IMPORTED_MODULE_0__.forEach)(path, (_part, isBracket, isArray) => {\n    let part = isBracket ? _part.slice(1, _part.length - 1) : _part;\n    schema = schema.resolve({\n      context,\n      parent,\n      value\n    });\n    let isTuple = schema.type === 'tuple';\n    let idx = isArray ? parseInt(part, 10) : 0;\n    if (schema.innerType || isTuple) {\n      if (isTuple && !isArray) throw new Error(`Yup.reach cannot implicitly index into a tuple type. the path part \"${lastPartDebug}\" must contain an index to the tuple element, e.g. \"${lastPartDebug}[0]\"`);\n      if (value && idx >= value.length) {\n        throw new Error(`Yup.reach cannot resolve an array item at index: ${_part}, in the path: ${path}. ` + `because there is no value at that index. `);\n      }\n      parent = value;\n      value = value && value[idx];\n      schema = isTuple ? schema.spec.types[idx] : schema.innerType;\n    }\n\n    // sometimes the array index part of a path doesn't exist: \"nested.arr.child\"\n    // in these cases the current part is the next schema and should be processed\n    // in this iteration. For cases where the index signature is included this\n    // check will fail and we'll handle the `child` part on the next iteration like normal\n    if (!isArray) {\n      if (!schema.fields || !schema.fields[part]) throw new Error(`The schema does not contain the path: ${path}. ` + `(failed at: ${lastPartDebug} which is a type: \"${schema.type}\")`);\n      parent = value;\n      value = value && value[part];\n      schema = schema.fields[part];\n    }\n    lastPart = part;\n    lastPartDebug = isBracket ? '[' + _part + ']' : '.' + _part;\n  });\n  return {\n    schema,\n    parent,\n    parentPath: lastPart\n  };\n}\nfunction reach(obj, path, value, context) {\n  return getIn(obj, path, value, context).schema;\n}\n\nclass ReferenceSet extends Set {\n  describe() {\n    const description = [];\n    for (const item of this.values()) {\n      description.push(Reference.isRef(item) ? item.describe() : item);\n    }\n    return description;\n  }\n  resolveAll(resolve) {\n    let result = [];\n    for (const item of this.values()) {\n      result.push(resolve(item));\n    }\n    return result;\n  }\n  clone() {\n    return new ReferenceSet(this.values());\n  }\n  merge(newItems, removeItems) {\n    const next = this.clone();\n    newItems.forEach(value => next.add(value));\n    removeItems.forEach(value => next.delete(value));\n    return next;\n  }\n}\n\n// tweaked from https://github.com/Kelin2025/nanoclone/blob/0abeb7635bda9b68ef2277093f76dbe3bf3948e1/src/index.js\nfunction clone(src, seen = new Map()) {\n  if (isSchema(src) || !src || typeof src !== 'object') return src;\n  if (seen.has(src)) return seen.get(src);\n  let copy;\n  if (src instanceof Date) {\n    // Date\n    copy = new Date(src.getTime());\n    seen.set(src, copy);\n  } else if (src instanceof RegExp) {\n    // RegExp\n    copy = new RegExp(src);\n    seen.set(src, copy);\n  } else if (Array.isArray(src)) {\n    // Array\n    copy = new Array(src.length);\n    seen.set(src, copy);\n    for (let i = 0; i < src.length; i++) copy[i] = clone(src[i], seen);\n  } else if (src instanceof Map) {\n    // Map\n    copy = new Map();\n    seen.set(src, copy);\n    for (const [k, v] of src.entries()) copy.set(k, clone(v, seen));\n  } else if (src instanceof Set) {\n    // Set\n    copy = new Set();\n    seen.set(src, copy);\n    for (const v of src) copy.add(clone(v, seen));\n  } else if (src instanceof Object) {\n    // Object\n    copy = {};\n    seen.set(src, copy);\n    for (const [k, v] of Object.entries(src)) copy[k] = clone(v, seen);\n  } else {\n    throw Error(`Unable to clone ${src}`);\n  }\n  return copy;\n}\n\n/**\n * Copied from @standard-schema/spec to avoid having a dependency on it.\n * https://github.com/standard-schema/standard-schema/blob/main/packages/spec/src/index.ts\n */\n\nfunction createStandardPath(path) {\n  if (!(path != null && path.length)) {\n    return undefined;\n  }\n\n  // Array to store the final path segments\n  const segments = [];\n  // Buffer for building the current segment\n  let currentSegment = '';\n  // Track if we're inside square brackets (array/property access)\n  let inBrackets = false;\n  // Track if we're inside quotes (for property names with special chars)\n  let inQuotes = false;\n  for (let i = 0; i < path.length; i++) {\n    const char = path[i];\n    if (char === '[' && !inQuotes) {\n      // When entering brackets, push any accumulated segment after splitting on dots\n      if (currentSegment) {\n        segments.push(...currentSegment.split('.').filter(Boolean));\n        currentSegment = '';\n      }\n      inBrackets = true;\n      continue;\n    }\n    if (char === ']' && !inQuotes) {\n      if (currentSegment) {\n        // Handle numeric indices (e.g. arr[0])\n        if (/^\\d+$/.test(currentSegment)) {\n          segments.push(currentSegment);\n        } else {\n          // Handle quoted property names (e.g. obj[\"foo.bar\"])\n          segments.push(currentSegment.replace(/^\"|\"$/g, ''));\n        }\n        currentSegment = '';\n      }\n      inBrackets = false;\n      continue;\n    }\n    if (char === '\"') {\n      // Toggle quote state for handling quoted property names\n      inQuotes = !inQuotes;\n      continue;\n    }\n    if (char === '.' && !inBrackets && !inQuotes) {\n      // On dots outside brackets/quotes, push current segment\n      if (currentSegment) {\n        segments.push(currentSegment);\n        currentSegment = '';\n      }\n      continue;\n    }\n    currentSegment += char;\n  }\n\n  // Push any remaining segment after splitting on dots\n  if (currentSegment) {\n    segments.push(...currentSegment.split('.').filter(Boolean));\n  }\n  return segments;\n}\nfunction createStandardIssues(error, parentPath) {\n  const path = parentPath ? `${parentPath}.${error.path}` : error.path;\n  return error.errors.map(err => ({\n    message: err,\n    path: createStandardPath(path)\n  }));\n}\nfunction issuesFromValidationError(error, parentPath) {\n  var _error$inner;\n  if (!((_error$inner = error.inner) != null && _error$inner.length) && error.errors.length) {\n    return createStandardIssues(error, parentPath);\n  }\n  const path = parentPath ? `${parentPath}.${error.path}` : error.path;\n  return error.inner.flatMap(err => issuesFromValidationError(err, path));\n}\n\n// If `CustomSchemaMeta` isn't extended with any keys, we'll fall back to a\n// loose Record definition allowing free form usage.\nclass Schema {\n  constructor(options) {\n    this.type = void 0;\n    this.deps = [];\n    this.tests = void 0;\n    this.transforms = void 0;\n    this.conditions = [];\n    this._mutate = void 0;\n    this.internalTests = {};\n    this._whitelist = new ReferenceSet();\n    this._blacklist = new ReferenceSet();\n    this.exclusiveTests = Object.create(null);\n    this._typeCheck = void 0;\n    this.spec = void 0;\n    this.tests = [];\n    this.transforms = [];\n    this.withMutation(() => {\n      this.typeError(mixed.notType);\n    });\n    this.type = options.type;\n    this._typeCheck = options.check;\n    this.spec = Object.assign({\n      strip: false,\n      strict: false,\n      abortEarly: true,\n      recursive: true,\n      disableStackTrace: false,\n      nullable: false,\n      optional: true,\n      coerce: true\n    }, options == null ? void 0 : options.spec);\n    this.withMutation(s => {\n      s.nonNullable();\n    });\n  }\n\n  // TODO: remove\n  get _type() {\n    return this.type;\n  }\n  clone(spec) {\n    if (this._mutate) {\n      if (spec) Object.assign(this.spec, spec);\n      return this;\n    }\n\n    // if the nested value is a schema we can skip cloning, since\n    // they are already immutable\n    const next = Object.create(Object.getPrototypeOf(this));\n\n    // @ts-expect-error this is readonly\n    next.type = this.type;\n    next._typeCheck = this._typeCheck;\n    next._whitelist = this._whitelist.clone();\n    next._blacklist = this._blacklist.clone();\n    next.internalTests = Object.assign({}, this.internalTests);\n    next.exclusiveTests = Object.assign({}, this.exclusiveTests);\n\n    // @ts-expect-error this is readonly\n    next.deps = [...this.deps];\n    next.conditions = [...this.conditions];\n    next.tests = [...this.tests];\n    next.transforms = [...this.transforms];\n    next.spec = clone(Object.assign({}, this.spec, spec));\n    return next;\n  }\n  label(label) {\n    let next = this.clone();\n    next.spec.label = label;\n    return next;\n  }\n  meta(...args) {\n    if (args.length === 0) return this.spec.meta;\n    let next = this.clone();\n    next.spec.meta = Object.assign(next.spec.meta || {}, args[0]);\n    return next;\n  }\n  withMutation(fn) {\n    let before = this._mutate;\n    this._mutate = true;\n    let result = fn(this);\n    this._mutate = before;\n    return result;\n  }\n  concat(schema) {\n    if (!schema || schema === this) return this;\n    if (schema.type !== this.type && this.type !== 'mixed') throw new TypeError(`You cannot \\`concat()\\` schema's of different types: ${this.type} and ${schema.type}`);\n    let base = this;\n    let combined = schema.clone();\n    const mergedSpec = Object.assign({}, base.spec, combined.spec);\n    combined.spec = mergedSpec;\n    combined.internalTests = Object.assign({}, base.internalTests, combined.internalTests);\n\n    // manually merge the blacklist/whitelist (the other `schema` takes\n    // precedence in case of conflicts)\n    combined._whitelist = base._whitelist.merge(schema._whitelist, schema._blacklist);\n    combined._blacklist = base._blacklist.merge(schema._blacklist, schema._whitelist);\n\n    // start with the current tests\n    combined.tests = base.tests;\n    combined.exclusiveTests = base.exclusiveTests;\n\n    // manually add the new tests to ensure\n    // the deduping logic is consistent\n    combined.withMutation(next => {\n      schema.tests.forEach(fn => {\n        next.test(fn.OPTIONS);\n      });\n    });\n    combined.transforms = [...base.transforms, ...combined.transforms];\n    return combined;\n  }\n  isType(v) {\n    if (v == null) {\n      if (this.spec.nullable && v === null) return true;\n      if (this.spec.optional && v === undefined) return true;\n      return false;\n    }\n    return this._typeCheck(v);\n  }\n  resolve(options) {\n    let schema = this;\n    if (schema.conditions.length) {\n      let conditions = schema.conditions;\n      schema = schema.clone();\n      schema.conditions = [];\n      schema = conditions.reduce((prevSchema, condition) => condition.resolve(prevSchema, options), schema);\n      schema = schema.resolve(options);\n    }\n    return schema;\n  }\n  resolveOptions(options) {\n    var _options$strict, _options$abortEarly, _options$recursive, _options$disableStack;\n    return Object.assign({}, options, {\n      from: options.from || [],\n      strict: (_options$strict = options.strict) != null ? _options$strict : this.spec.strict,\n      abortEarly: (_options$abortEarly = options.abortEarly) != null ? _options$abortEarly : this.spec.abortEarly,\n      recursive: (_options$recursive = options.recursive) != null ? _options$recursive : this.spec.recursive,\n      disableStackTrace: (_options$disableStack = options.disableStackTrace) != null ? _options$disableStack : this.spec.disableStackTrace\n    });\n  }\n\n  /**\n   * Run the configured transform pipeline over an input value.\n   */\n\n  cast(value, options = {}) {\n    let resolvedSchema = this.resolve(Object.assign({\n      value\n    }, options));\n    let allowOptionality = options.assert === 'ignore-optionality';\n    let result = resolvedSchema._cast(value, options);\n    if (options.assert !== false && !resolvedSchema.isType(result)) {\n      if (allowOptionality && isAbsent(result)) {\n        return result;\n      }\n      let formattedValue = printValue(value);\n      let formattedResult = printValue(result);\n      throw new TypeError(`The value of ${options.path || 'field'} could not be cast to a value ` + `that satisfies the schema type: \"${resolvedSchema.type}\". \\n\\n` + `attempted value: ${formattedValue} \\n` + (formattedResult !== formattedValue ? `result of cast: ${formattedResult}` : ''));\n    }\n    return result;\n  }\n  _cast(rawValue, options) {\n    let value = rawValue === undefined ? rawValue : this.transforms.reduce((prevValue, fn) => fn.call(this, prevValue, rawValue, this), rawValue);\n    if (value === undefined) {\n      value = this.getDefault(options);\n    }\n    return value;\n  }\n  _validate(_value, options = {}, panic, next) {\n    let {\n      path,\n      originalValue = _value,\n      strict = this.spec.strict\n    } = options;\n    let value = _value;\n    if (!strict) {\n      value = this._cast(value, Object.assign({\n        assert: false\n      }, options));\n    }\n    let initialTests = [];\n    for (let test of Object.values(this.internalTests)) {\n      if (test) initialTests.push(test);\n    }\n    this.runTests({\n      path,\n      value,\n      originalValue,\n      options,\n      tests: initialTests\n    }, panic, initialErrors => {\n      // even if we aren't ending early we can't proceed further if the types aren't correct\n      if (initialErrors.length) {\n        return next(initialErrors, value);\n      }\n      this.runTests({\n        path,\n        value,\n        originalValue,\n        options,\n        tests: this.tests\n      }, panic, next);\n    });\n  }\n\n  /**\n   * Executes a set of validations, either schema, produced Tests or a nested\n   * schema validate result.\n   */\n  runTests(runOptions, panic, next) {\n    let fired = false;\n    let {\n      tests,\n      value,\n      originalValue,\n      path,\n      options\n    } = runOptions;\n    let panicOnce = arg => {\n      if (fired) return;\n      fired = true;\n      panic(arg, value);\n    };\n    let nextOnce = arg => {\n      if (fired) return;\n      fired = true;\n      next(arg, value);\n    };\n    let count = tests.length;\n    let nestedErrors = [];\n    if (!count) return nextOnce([]);\n    let args = {\n      value,\n      originalValue,\n      path,\n      options,\n      schema: this\n    };\n    for (let i = 0; i < tests.length; i++) {\n      const test = tests[i];\n      test(args, panicOnce, function finishTestRun(err) {\n        if (err) {\n          Array.isArray(err) ? nestedErrors.push(...err) : nestedErrors.push(err);\n        }\n        if (--count <= 0) {\n          nextOnce(nestedErrors);\n        }\n      });\n    }\n  }\n  asNestedTest({\n    key,\n    index,\n    parent,\n    parentPath,\n    originalParent,\n    options\n  }) {\n    const k = key != null ? key : index;\n    if (k == null) {\n      throw TypeError('Must include `key` or `index` for nested validations');\n    }\n    const isIndex = typeof k === 'number';\n    let value = parent[k];\n    const testOptions = Object.assign({}, options, {\n      // Nested validations fields are always strict:\n      //    1. parent isn't strict so the casting will also have cast inner values\n      //    2. parent is strict in which case the nested values weren't cast either\n      strict: true,\n      parent,\n      value,\n      originalValue: originalParent[k],\n      // FIXME: tests depend on `index` being passed around deeply,\n      //   we should not let the options.key/index bleed through\n      key: undefined,\n      // index: undefined,\n      [isIndex ? 'index' : 'key']: k,\n      path: isIndex || k.includes('.') ? `${parentPath || ''}[${isIndex ? k : `\"${k}\"`}]` : (parentPath ? `${parentPath}.` : '') + key\n    });\n    return (_, panic, next) => this.resolve(testOptions)._validate(value, testOptions, panic, next);\n  }\n  validate(value, options) {\n    var _options$disableStack2;\n    let schema = this.resolve(Object.assign({}, options, {\n      value\n    }));\n    let disableStackTrace = (_options$disableStack2 = options == null ? void 0 : options.disableStackTrace) != null ? _options$disableStack2 : schema.spec.disableStackTrace;\n    return new Promise((resolve, reject) => schema._validate(value, options, (error, parsed) => {\n      if (ValidationError.isError(error)) error.value = parsed;\n      reject(error);\n    }, (errors, validated) => {\n      if (errors.length) reject(new ValidationError(errors, validated, undefined, undefined, disableStackTrace));else resolve(validated);\n    }));\n  }\n  validateSync(value, options) {\n    var _options$disableStack3;\n    let schema = this.resolve(Object.assign({}, options, {\n      value\n    }));\n    let result;\n    let disableStackTrace = (_options$disableStack3 = options == null ? void 0 : options.disableStackTrace) != null ? _options$disableStack3 : schema.spec.disableStackTrace;\n    schema._validate(value, Object.assign({}, options, {\n      sync: true\n    }), (error, parsed) => {\n      if (ValidationError.isError(error)) error.value = parsed;\n      throw error;\n    }, (errors, validated) => {\n      if (errors.length) throw new ValidationError(errors, value, undefined, undefined, disableStackTrace);\n      result = validated;\n    });\n    return result;\n  }\n  isValid(value, options) {\n    return this.validate(value, options).then(() => true, err => {\n      if (ValidationError.isError(err)) return false;\n      throw err;\n    });\n  }\n  isValidSync(value, options) {\n    try {\n      this.validateSync(value, options);\n      return true;\n    } catch (err) {\n      if (ValidationError.isError(err)) return false;\n      throw err;\n    }\n  }\n  _getDefault(options) {\n    let defaultValue = this.spec.default;\n    if (defaultValue == null) {\n      return defaultValue;\n    }\n    return typeof defaultValue === 'function' ? defaultValue.call(this, options) : clone(defaultValue);\n  }\n  getDefault(options\n  // If schema is defaulted we know it's at least not undefined\n  ) {\n    let schema = this.resolve(options || {});\n    return schema._getDefault(options);\n  }\n  default(def) {\n    if (arguments.length === 0) {\n      return this._getDefault();\n    }\n    let next = this.clone({\n      default: def\n    });\n    return next;\n  }\n  strict(isStrict = true) {\n    return this.clone({\n      strict: isStrict\n    });\n  }\n  nullability(nullable, message) {\n    const next = this.clone({\n      nullable\n    });\n    next.internalTests.nullable = createValidation({\n      message,\n      name: 'nullable',\n      test(value) {\n        return value === null ? this.schema.spec.nullable : true;\n      }\n    });\n    return next;\n  }\n  optionality(optional, message) {\n    const next = this.clone({\n      optional\n    });\n    next.internalTests.optionality = createValidation({\n      message,\n      name: 'optionality',\n      test(value) {\n        return value === undefined ? this.schema.spec.optional : true;\n      }\n    });\n    return next;\n  }\n  optional() {\n    return this.optionality(true);\n  }\n  defined(message = mixed.defined) {\n    return this.optionality(false, message);\n  }\n  nullable() {\n    return this.nullability(true);\n  }\n  nonNullable(message = mixed.notNull) {\n    return this.nullability(false, message);\n  }\n  required(message = mixed.required) {\n    return this.clone().withMutation(next => next.nonNullable(message).defined(message));\n  }\n  notRequired() {\n    return this.clone().withMutation(next => next.nullable().optional());\n  }\n  transform(fn) {\n    let next = this.clone();\n    next.transforms.push(fn);\n    return next;\n  }\n\n  /**\n   * Adds a test function to the schema's queue of tests.\n   * tests can be exclusive or non-exclusive.\n   *\n   * - exclusive tests, will replace any existing tests of the same name.\n   * - non-exclusive: can be stacked\n   *\n   * If a non-exclusive test is added to a schema with an exclusive test of the same name\n   * the exclusive test is removed and further tests of the same name will be stacked.\n   *\n   * If an exclusive test is added to a schema with non-exclusive tests of the same name\n   * the previous tests are removed and further tests of the same name will replace each other.\n   */\n\n  test(...args) {\n    let opts;\n    if (args.length === 1) {\n      if (typeof args[0] === 'function') {\n        opts = {\n          test: args[0]\n        };\n      } else {\n        opts = args[0];\n      }\n    } else if (args.length === 2) {\n      opts = {\n        name: args[0],\n        test: args[1]\n      };\n    } else {\n      opts = {\n        name: args[0],\n        message: args[1],\n        test: args[2]\n      };\n    }\n    if (opts.message === undefined) opts.message = mixed.default;\n    if (typeof opts.test !== 'function') throw new TypeError('`test` is a required parameters');\n    let next = this.clone();\n    let validate = createValidation(opts);\n    let isExclusive = opts.exclusive || opts.name && next.exclusiveTests[opts.name] === true;\n    if (opts.exclusive) {\n      if (!opts.name) throw new TypeError('Exclusive tests must provide a unique `name` identifying the test');\n    }\n    if (opts.name) next.exclusiveTests[opts.name] = !!opts.exclusive;\n    next.tests = next.tests.filter(fn => {\n      if (fn.OPTIONS.name === opts.name) {\n        if (isExclusive) return false;\n        if (fn.OPTIONS.test === validate.OPTIONS.test) return false;\n      }\n      return true;\n    });\n    next.tests.push(validate);\n    return next;\n  }\n  when(keys, options) {\n    if (!Array.isArray(keys) && typeof keys !== 'string') {\n      options = keys;\n      keys = '.';\n    }\n    let next = this.clone();\n    let deps = toArray(keys).map(key => new Reference(key));\n    deps.forEach(dep => {\n      // @ts-ignore readonly array\n      if (dep.isSibling) next.deps.push(dep.key);\n    });\n    next.conditions.push(typeof options === 'function' ? new Condition(deps, options) : Condition.fromOptions(deps, options));\n    return next;\n  }\n  typeError(message) {\n    let next = this.clone();\n    next.internalTests.typeError = createValidation({\n      message,\n      name: 'typeError',\n      skipAbsent: true,\n      test(value) {\n        if (!this.schema._typeCheck(value)) return this.createError({\n          params: {\n            type: this.schema.type\n          }\n        });\n        return true;\n      }\n    });\n    return next;\n  }\n  oneOf(enums, message = mixed.oneOf) {\n    let next = this.clone();\n    enums.forEach(val => {\n      next._whitelist.add(val);\n      next._blacklist.delete(val);\n    });\n    next.internalTests.whiteList = createValidation({\n      message,\n      name: 'oneOf',\n      skipAbsent: true,\n      test(value) {\n        let valids = this.schema._whitelist;\n        let resolved = valids.resolveAll(this.resolve);\n        return resolved.includes(value) ? true : this.createError({\n          params: {\n            values: Array.from(valids).join(', '),\n            resolved\n          }\n        });\n      }\n    });\n    return next;\n  }\n  notOneOf(enums, message = mixed.notOneOf) {\n    let next = this.clone();\n    enums.forEach(val => {\n      next._blacklist.add(val);\n      next._whitelist.delete(val);\n    });\n    next.internalTests.blacklist = createValidation({\n      message,\n      name: 'notOneOf',\n      test(value) {\n        let invalids = this.schema._blacklist;\n        let resolved = invalids.resolveAll(this.resolve);\n        if (resolved.includes(value)) return this.createError({\n          params: {\n            values: Array.from(invalids).join(', '),\n            resolved\n          }\n        });\n        return true;\n      }\n    });\n    return next;\n  }\n  strip(strip = true) {\n    let next = this.clone();\n    next.spec.strip = strip;\n    return next;\n  }\n\n  /**\n   * Return a serialized description of the schema including validations, flags, types etc.\n   *\n   * @param options Provide any needed context for resolving runtime schema alterations (lazy, when conditions, etc).\n   */\n  describe(options) {\n    const next = (options ? this.resolve(options) : this).clone();\n    const {\n      label,\n      meta,\n      optional,\n      nullable\n    } = next.spec;\n    const description = {\n      meta,\n      label,\n      optional,\n      nullable,\n      default: next.getDefault(options),\n      type: next.type,\n      oneOf: next._whitelist.describe(),\n      notOneOf: next._blacklist.describe(),\n      tests: next.tests.filter((n, idx, list) => list.findIndex(c => c.OPTIONS.name === n.OPTIONS.name) === idx).map(fn => {\n        const params = fn.OPTIONS.params && options ? resolveParams(Object.assign({}, fn.OPTIONS.params), options) : fn.OPTIONS.params;\n        return {\n          name: fn.OPTIONS.name,\n          params\n        };\n      })\n    };\n    return description;\n  }\n  get ['~standard']() {\n    const schema = this;\n    const standard = {\n      version: 1,\n      vendor: 'yup',\n      async validate(value) {\n        try {\n          const result = await schema.validate(value, {\n            abortEarly: false\n          });\n          return {\n            value: result\n          };\n        } catch (err) {\n          if (err instanceof ValidationError) {\n            return {\n              issues: issuesFromValidationError(err)\n            };\n          }\n          throw err;\n        }\n      }\n    };\n    return standard;\n  }\n}\n// @ts-expect-error\nSchema.prototype.__isYupSchema__ = true;\nfor (const method of ['validate', 'validateSync']) Schema.prototype[`${method}At`] = function (path, value, options = {}) {\n  const {\n    parent,\n    parentPath,\n    schema\n  } = getIn(this, path, value, options.context);\n  return schema[method](parent && parent[parentPath], Object.assign({}, options, {\n    parent,\n    path\n  }));\n};\nfor (const alias of ['equals', 'is']) Schema.prototype[alias] = Schema.prototype.oneOf;\nfor (const alias of ['not', 'nope']) Schema.prototype[alias] = Schema.prototype.notOneOf;\n\nconst returnsTrue = () => true;\nfunction create$8(spec) {\n  return new MixedSchema(spec);\n}\nclass MixedSchema extends Schema {\n  constructor(spec) {\n    super(typeof spec === 'function' ? {\n      type: 'mixed',\n      check: spec\n    } : Object.assign({\n      type: 'mixed',\n      check: returnsTrue\n    }, spec));\n  }\n}\ncreate$8.prototype = MixedSchema.prototype;\n\nfunction create$7() {\n  return new BooleanSchema();\n}\nclass BooleanSchema extends Schema {\n  constructor() {\n    super({\n      type: 'boolean',\n      check(v) {\n        if (v instanceof Boolean) v = v.valueOf();\n        return typeof v === 'boolean';\n      }\n    });\n    this.withMutation(() => {\n      this.transform((value, _raw, ctx) => {\n        if (ctx.spec.coerce && !ctx.isType(value)) {\n          if (/^(true|1)$/i.test(String(value))) return true;\n          if (/^(false|0)$/i.test(String(value))) return false;\n        }\n        return value;\n      });\n    });\n  }\n  isTrue(message = boolean.isValue) {\n    return this.test({\n      message,\n      name: 'is-value',\n      exclusive: true,\n      params: {\n        value: 'true'\n      },\n      test(value) {\n        return isAbsent(value) || value === true;\n      }\n    });\n  }\n  isFalse(message = boolean.isValue) {\n    return this.test({\n      message,\n      name: 'is-value',\n      exclusive: true,\n      params: {\n        value: 'false'\n      },\n      test(value) {\n        return isAbsent(value) || value === false;\n      }\n    });\n  }\n  default(def) {\n    return super.default(def);\n  }\n  defined(msg) {\n    return super.defined(msg);\n  }\n  optional() {\n    return super.optional();\n  }\n  required(msg) {\n    return super.required(msg);\n  }\n  notRequired() {\n    return super.notRequired();\n  }\n  nullable() {\n    return super.nullable();\n  }\n  nonNullable(msg) {\n    return super.nonNullable(msg);\n  }\n  strip(v) {\n    return super.strip(v);\n  }\n}\ncreate$7.prototype = BooleanSchema.prototype;\n\n/**\n * This file is a modified version of the file from the following repository:\n * Date.parse with progressive enhancement for ISO 8601 <https://github.com/csnover/js-iso8601>\n * NON-CONFORMANT EDITION.\n * © 2011 Colin Snover <http://zetafleet.com>\n * Released under MIT license.\n */\n\n// prettier-ignore\n//                1 YYYY                2 MM        3 DD              4 HH     5 mm        6 ss           7 msec         8 Z 9 ±   10 tzHH    11 tzmm\nconst isoReg = /^(\\d{4}|[+-]\\d{6})(?:-?(\\d{2})(?:-?(\\d{2}))?)?(?:[ T]?(\\d{2}):?(\\d{2})(?::?(\\d{2})(?:[,.](\\d{1,}))?)?(?:(Z)|([+-])(\\d{2})(?::?(\\d{2}))?)?)?$/;\nfunction parseIsoDate(date) {\n  const struct = parseDateStruct(date);\n  if (!struct) return Date.parse ? Date.parse(date) : Number.NaN;\n\n  // timestamps without timezone identifiers should be considered local time\n  if (struct.z === undefined && struct.plusMinus === undefined) {\n    return new Date(struct.year, struct.month, struct.day, struct.hour, struct.minute, struct.second, struct.millisecond).valueOf();\n  }\n  let totalMinutesOffset = 0;\n  if (struct.z !== 'Z' && struct.plusMinus !== undefined) {\n    totalMinutesOffset = struct.hourOffset * 60 + struct.minuteOffset;\n    if (struct.plusMinus === '+') totalMinutesOffset = 0 - totalMinutesOffset;\n  }\n  return Date.UTC(struct.year, struct.month, struct.day, struct.hour, struct.minute + totalMinutesOffset, struct.second, struct.millisecond);\n}\nfunction parseDateStruct(date) {\n  var _regexResult$7$length, _regexResult$;\n  const regexResult = isoReg.exec(date);\n  if (!regexResult) return null;\n\n  // use of toNumber() avoids NaN timestamps caused by “undefined”\n  // values being passed to Date constructor\n  return {\n    year: toNumber(regexResult[1]),\n    month: toNumber(regexResult[2], 1) - 1,\n    day: toNumber(regexResult[3], 1),\n    hour: toNumber(regexResult[4]),\n    minute: toNumber(regexResult[5]),\n    second: toNumber(regexResult[6]),\n    millisecond: regexResult[7] ?\n    // allow arbitrary sub-second precision beyond milliseconds\n    toNumber(regexResult[7].substring(0, 3)) : 0,\n    precision: (_regexResult$7$length = (_regexResult$ = regexResult[7]) == null ? void 0 : _regexResult$.length) != null ? _regexResult$7$length : undefined,\n    z: regexResult[8] || undefined,\n    plusMinus: regexResult[9] || undefined,\n    hourOffset: toNumber(regexResult[10]),\n    minuteOffset: toNumber(regexResult[11])\n  };\n}\nfunction toNumber(str, defaultValue = 0) {\n  return Number(str) || defaultValue;\n}\n\n// Taken from HTML spec: https://html.spec.whatwg.org/multipage/input.html#valid-e-mail-address\nlet rEmail =\n// eslint-disable-next-line\n/^[a-zA-Z0-9.!#$%&'*+\\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;\nlet rUrl =\n// eslint-disable-next-line\n/^((https?|ftp):)?\\/\\/(((([a-z]|\\d|-|\\.|_|~|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])|(%[\\da-f]{2})|[!\\$&'\\(\\)\\*\\+,;=]|:)*@)?(((\\d|[1-9]\\d|1\\d\\d|2[0-4]\\d|25[0-5])\\.(\\d|[1-9]\\d|1\\d\\d|2[0-4]\\d|25[0-5])\\.(\\d|[1-9]\\d|1\\d\\d|2[0-4]\\d|25[0-5])\\.(\\d|[1-9]\\d|1\\d\\d|2[0-4]\\d|25[0-5]))|((([a-z]|\\d|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])|(([a-z]|\\d|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])([a-z]|\\d|-|\\.|_|~|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])*([a-z]|\\d|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])))\\.)+(([a-z]|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])|(([a-z]|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])([a-z]|\\d|-|\\.|_|~|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])*([a-z]|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])))\\.?)(:\\d*)?)(\\/((([a-z]|\\d|-|\\.|_|~|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])|(%[\\da-f]{2})|[!\\$&'\\(\\)\\*\\+,;=]|:|@)+(\\/(([a-z]|\\d|-|\\.|_|~|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])|(%[\\da-f]{2})|[!\\$&'\\(\\)\\*\\+,;=]|:|@)*)*)?)?(\\?((([a-z]|\\d|-|\\.|_|~|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])|(%[\\da-f]{2})|[!\\$&'\\(\\)\\*\\+,;=]|:|@)|[\\uE000-\\uF8FF]|\\/|\\?)*)?(\\#((([a-z]|\\d|-|\\.|_|~|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])|(%[\\da-f]{2})|[!\\$&'\\(\\)\\*\\+,;=]|:|@)|\\/|\\?)*)?$/i;\n\n// eslint-disable-next-line\nlet rUUID = /^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i;\nlet yearMonthDay = '^\\\\d{4}-\\\\d{2}-\\\\d{2}';\nlet hourMinuteSecond = '\\\\d{2}:\\\\d{2}:\\\\d{2}';\nlet zOrOffset = '(([+-]\\\\d{2}(:?\\\\d{2})?)|Z)';\nlet rIsoDateTime = new RegExp(`${yearMonthDay}T${hourMinuteSecond}(\\\\.\\\\d+)?${zOrOffset}$`);\nlet isTrimmed = value => isAbsent(value) || value === value.trim();\nlet objStringTag = {}.toString();\nfunction create$6() {\n  return new StringSchema();\n}\nclass StringSchema extends Schema {\n  constructor() {\n    super({\n      type: 'string',\n      check(value) {\n        if (value instanceof String) value = value.valueOf();\n        return typeof value === 'string';\n      }\n    });\n    this.withMutation(() => {\n      this.transform((value, _raw, ctx) => {\n        if (!ctx.spec.coerce || ctx.isType(value)) return value;\n\n        // don't ever convert arrays\n        if (Array.isArray(value)) return value;\n        const strValue = value != null && value.toString ? value.toString() : value;\n\n        // no one wants plain objects converted to [Object object]\n        if (strValue === objStringTag) return value;\n        return strValue;\n      });\n    });\n  }\n  required(message) {\n    return super.required(message).withMutation(schema => schema.test({\n      message: message || mixed.required,\n      name: 'required',\n      skipAbsent: true,\n      test: value => !!value.length\n    }));\n  }\n  notRequired() {\n    return super.notRequired().withMutation(schema => {\n      schema.tests = schema.tests.filter(t => t.OPTIONS.name !== 'required');\n      return schema;\n    });\n  }\n  length(length, message = string.length) {\n    return this.test({\n      message,\n      name: 'length',\n      exclusive: true,\n      params: {\n        length\n      },\n      skipAbsent: true,\n      test(value) {\n        return value.length === this.resolve(length);\n      }\n    });\n  }\n  min(min, message = string.min) {\n    return this.test({\n      message,\n      name: 'min',\n      exclusive: true,\n      params: {\n        min\n      },\n      skipAbsent: true,\n      test(value) {\n        return value.length >= this.resolve(min);\n      }\n    });\n  }\n  max(max, message = string.max) {\n    return this.test({\n      name: 'max',\n      exclusive: true,\n      message,\n      params: {\n        max\n      },\n      skipAbsent: true,\n      test(value) {\n        return value.length <= this.resolve(max);\n      }\n    });\n  }\n  matches(regex, options) {\n    let excludeEmptyString = false;\n    let message;\n    let name;\n    if (options) {\n      if (typeof options === 'object') {\n        ({\n          excludeEmptyString = false,\n          message,\n          name\n        } = options);\n      } else {\n        message = options;\n      }\n    }\n    return this.test({\n      name: name || 'matches',\n      message: message || string.matches,\n      params: {\n        regex\n      },\n      skipAbsent: true,\n      test: value => value === '' && excludeEmptyString || value.search(regex) !== -1\n    });\n  }\n  email(message = string.email) {\n    return this.matches(rEmail, {\n      name: 'email',\n      message,\n      excludeEmptyString: true\n    });\n  }\n  url(message = string.url) {\n    return this.matches(rUrl, {\n      name: 'url',\n      message,\n      excludeEmptyString: true\n    });\n  }\n  uuid(message = string.uuid) {\n    return this.matches(rUUID, {\n      name: 'uuid',\n      message,\n      excludeEmptyString: false\n    });\n  }\n  datetime(options) {\n    let message = '';\n    let allowOffset;\n    let precision;\n    if (options) {\n      if (typeof options === 'object') {\n        ({\n          message = '',\n          allowOffset = false,\n          precision = undefined\n        } = options);\n      } else {\n        message = options;\n      }\n    }\n    return this.matches(rIsoDateTime, {\n      name: 'datetime',\n      message: message || string.datetime,\n      excludeEmptyString: true\n    }).test({\n      name: 'datetime_offset',\n      message: message || string.datetime_offset,\n      params: {\n        allowOffset\n      },\n      skipAbsent: true,\n      test: value => {\n        if (!value || allowOffset) return true;\n        const struct = parseDateStruct(value);\n        if (!struct) return false;\n        return !!struct.z;\n      }\n    }).test({\n      name: 'datetime_precision',\n      message: message || string.datetime_precision,\n      params: {\n        precision\n      },\n      skipAbsent: true,\n      test: value => {\n        if (!value || precision == undefined) return true;\n        const struct = parseDateStruct(value);\n        if (!struct) return false;\n        return struct.precision === precision;\n      }\n    });\n  }\n\n  //-- transforms --\n  ensure() {\n    return this.default('').transform(val => val === null ? '' : val);\n  }\n  trim(message = string.trim) {\n    return this.transform(val => val != null ? val.trim() : val).test({\n      message,\n      name: 'trim',\n      test: isTrimmed\n    });\n  }\n  lowercase(message = string.lowercase) {\n    return this.transform(value => !isAbsent(value) ? value.toLowerCase() : value).test({\n      message,\n      name: 'string_case',\n      exclusive: true,\n      skipAbsent: true,\n      test: value => isAbsent(value) || value === value.toLowerCase()\n    });\n  }\n  uppercase(message = string.uppercase) {\n    return this.transform(value => !isAbsent(value) ? value.toUpperCase() : value).test({\n      message,\n      name: 'string_case',\n      exclusive: true,\n      skipAbsent: true,\n      test: value => isAbsent(value) || value === value.toUpperCase()\n    });\n  }\n}\ncreate$6.prototype = StringSchema.prototype;\n\n//\n// String Interfaces\n//\n\nlet isNaN$1 = value => value != +value;\nfunction create$5() {\n  return new NumberSchema();\n}\nclass NumberSchema extends Schema {\n  constructor() {\n    super({\n      type: 'number',\n      check(value) {\n        if (value instanceof Number) value = value.valueOf();\n        return typeof value === 'number' && !isNaN$1(value);\n      }\n    });\n    this.withMutation(() => {\n      this.transform((value, _raw, ctx) => {\n        if (!ctx.spec.coerce) return value;\n        let parsed = value;\n        if (typeof parsed === 'string') {\n          parsed = parsed.replace(/\\s/g, '');\n          if (parsed === '') return NaN;\n          // don't use parseFloat to avoid positives on alpha-numeric strings\n          parsed = +parsed;\n        }\n\n        // null -> NaN isn't useful; treat all nulls as null and let it fail on\n        // nullability check vs TypeErrors\n        if (ctx.isType(parsed) || parsed === null) return parsed;\n        return parseFloat(parsed);\n      });\n    });\n  }\n  min(min, message = number.min) {\n    return this.test({\n      message,\n      name: 'min',\n      exclusive: true,\n      params: {\n        min\n      },\n      skipAbsent: true,\n      test(value) {\n        return value >= this.resolve(min);\n      }\n    });\n  }\n  max(max, message = number.max) {\n    return this.test({\n      message,\n      name: 'max',\n      exclusive: true,\n      params: {\n        max\n      },\n      skipAbsent: true,\n      test(value) {\n        return value <= this.resolve(max);\n      }\n    });\n  }\n  lessThan(less, message = number.lessThan) {\n    return this.test({\n      message,\n      name: 'max',\n      exclusive: true,\n      params: {\n        less\n      },\n      skipAbsent: true,\n      test(value) {\n        return value < this.resolve(less);\n      }\n    });\n  }\n  moreThan(more, message = number.moreThan) {\n    return this.test({\n      message,\n      name: 'min',\n      exclusive: true,\n      params: {\n        more\n      },\n      skipAbsent: true,\n      test(value) {\n        return value > this.resolve(more);\n      }\n    });\n  }\n  positive(msg = number.positive) {\n    return this.moreThan(0, msg);\n  }\n  negative(msg = number.negative) {\n    return this.lessThan(0, msg);\n  }\n  integer(message = number.integer) {\n    return this.test({\n      name: 'integer',\n      message,\n      skipAbsent: true,\n      test: val => Number.isInteger(val)\n    });\n  }\n  truncate() {\n    return this.transform(value => !isAbsent(value) ? value | 0 : value);\n  }\n  round(method) {\n    var _method;\n    let avail = ['ceil', 'floor', 'round', 'trunc'];\n    method = ((_method = method) == null ? void 0 : _method.toLowerCase()) || 'round';\n\n    // this exists for symemtry with the new Math.trunc\n    if (method === 'trunc') return this.truncate();\n    if (avail.indexOf(method.toLowerCase()) === -1) throw new TypeError('Only valid options for round() are: ' + avail.join(', '));\n    return this.transform(value => !isAbsent(value) ? Math[method](value) : value);\n  }\n}\ncreate$5.prototype = NumberSchema.prototype;\n\n//\n// Number Interfaces\n//\n\nlet invalidDate = new Date('');\nlet isDate = obj => Object.prototype.toString.call(obj) === '[object Date]';\nfunction create$4() {\n  return new DateSchema();\n}\nclass DateSchema extends Schema {\n  constructor() {\n    super({\n      type: 'date',\n      check(v) {\n        return isDate(v) && !isNaN(v.getTime());\n      }\n    });\n    this.withMutation(() => {\n      this.transform((value, _raw, ctx) => {\n        // null -> InvalidDate isn't useful; treat all nulls as null and let it fail on\n        // nullability check vs TypeErrors\n        if (!ctx.spec.coerce || ctx.isType(value) || value === null) return value;\n        value = parseIsoDate(value);\n\n        // 0 is a valid timestamp equivalent to 1970-01-01T00:00:00Z(unix epoch) or before.\n        return !isNaN(value) ? new Date(value) : DateSchema.INVALID_DATE;\n      });\n    });\n  }\n  prepareParam(ref, name) {\n    let param;\n    if (!Reference.isRef(ref)) {\n      let cast = this.cast(ref);\n      if (!this._typeCheck(cast)) throw new TypeError(`\\`${name}\\` must be a Date or a value that can be \\`cast()\\` to a Date`);\n      param = cast;\n    } else {\n      param = ref;\n    }\n    return param;\n  }\n  min(min, message = date.min) {\n    let limit = this.prepareParam(min, 'min');\n    return this.test({\n      message,\n      name: 'min',\n      exclusive: true,\n      params: {\n        min\n      },\n      skipAbsent: true,\n      test(value) {\n        return value >= this.resolve(limit);\n      }\n    });\n  }\n  max(max, message = date.max) {\n    let limit = this.prepareParam(max, 'max');\n    return this.test({\n      message,\n      name: 'max',\n      exclusive: true,\n      params: {\n        max\n      },\n      skipAbsent: true,\n      test(value) {\n        return value <= this.resolve(limit);\n      }\n    });\n  }\n}\nDateSchema.INVALID_DATE = invalidDate;\ncreate$4.prototype = DateSchema.prototype;\ncreate$4.INVALID_DATE = invalidDate;\n\n// @ts-expect-error\nfunction sortFields(fields, excludedEdges = []) {\n  let edges = [];\n  let nodes = new Set();\n  let excludes = new Set(excludedEdges.map(([a, b]) => `${a}-${b}`));\n  function addNode(depPath, key) {\n    let node = (0,property_expr__WEBPACK_IMPORTED_MODULE_0__.split)(depPath)[0];\n    nodes.add(node);\n    if (!excludes.has(`${key}-${node}`)) edges.push([key, node]);\n  }\n  for (const key of Object.keys(fields)) {\n    let value = fields[key];\n    nodes.add(key);\n    if (Reference.isRef(value) && value.isSibling) addNode(value.path, key);else if (isSchema(value) && 'deps' in value) value.deps.forEach(path => addNode(path, key));\n  }\n  return toposort__WEBPACK_IMPORTED_MODULE_2___default().array(Array.from(nodes), edges).reverse();\n}\n\nfunction findIndex(arr, err) {\n  let idx = Infinity;\n  arr.some((key, ii) => {\n    var _err$path;\n    if ((_err$path = err.path) != null && _err$path.includes(key)) {\n      idx = ii;\n      return true;\n    }\n  });\n  return idx;\n}\nfunction sortByKeyOrder(keys) {\n  return (a, b) => {\n    return findIndex(keys, a) - findIndex(keys, b);\n  };\n}\n\nconst parseJson = (value, _, ctx) => {\n  if (typeof value !== 'string') {\n    return value;\n  }\n  let parsed = value;\n  try {\n    parsed = JSON.parse(value);\n  } catch (err) {\n    /* */\n  }\n  return ctx.isType(parsed) ? parsed : value;\n};\n\n// @ts-ignore\nfunction deepPartial(schema) {\n  if ('fields' in schema) {\n    const partial = {};\n    for (const [key, fieldSchema] of Object.entries(schema.fields)) {\n      partial[key] = deepPartial(fieldSchema);\n    }\n    return schema.setFields(partial);\n  }\n  if (schema.type === 'array') {\n    const nextArray = schema.optional();\n    if (nextArray.innerType) nextArray.innerType = deepPartial(nextArray.innerType);\n    return nextArray;\n  }\n  if (schema.type === 'tuple') {\n    return schema.optional().clone({\n      types: schema.spec.types.map(deepPartial)\n    });\n  }\n  if ('optional' in schema) {\n    return schema.optional();\n  }\n  return schema;\n}\nconst deepHas = (obj, p) => {\n  const path = [...(0,property_expr__WEBPACK_IMPORTED_MODULE_0__.normalizePath)(p)];\n  if (path.length === 1) return path[0] in obj;\n  let last = path.pop();\n  let parent = (0,property_expr__WEBPACK_IMPORTED_MODULE_0__.getter)((0,property_expr__WEBPACK_IMPORTED_MODULE_0__.join)(path), true)(obj);\n  return !!(parent && last in parent);\n};\nlet isObject = obj => Object.prototype.toString.call(obj) === '[object Object]';\nfunction unknown(ctx, value) {\n  let known = Object.keys(ctx.fields);\n  return Object.keys(value).filter(key => known.indexOf(key) === -1);\n}\nconst defaultSort = sortByKeyOrder([]);\nfunction create$3(spec) {\n  return new ObjectSchema(spec);\n}\nclass ObjectSchema extends Schema {\n  constructor(spec) {\n    super({\n      type: 'object',\n      check(value) {\n        return isObject(value) || typeof value === 'function';\n      }\n    });\n    this.fields = Object.create(null);\n    this._sortErrors = defaultSort;\n    this._nodes = [];\n    this._excludedEdges = [];\n    this.withMutation(() => {\n      if (spec) {\n        this.shape(spec);\n      }\n    });\n  }\n  _cast(_value, options = {}) {\n    var _options$stripUnknown;\n    let value = super._cast(_value, options);\n\n    //should ignore nulls here\n    if (value === undefined) return this.getDefault(options);\n    if (!this._typeCheck(value)) return value;\n    let fields = this.fields;\n    let strip = (_options$stripUnknown = options.stripUnknown) != null ? _options$stripUnknown : this.spec.noUnknown;\n    let props = [].concat(this._nodes, Object.keys(value).filter(v => !this._nodes.includes(v)));\n    let intermediateValue = {}; // is filled during the transform below\n    let innerOptions = Object.assign({}, options, {\n      parent: intermediateValue,\n      __validating: options.__validating || false\n    });\n    let isChanged = false;\n    for (const prop of props) {\n      let field = fields[prop];\n      let exists = (prop in value);\n      if (field) {\n        let fieldValue;\n        let inputValue = value[prop];\n\n        // safe to mutate since this is fired in sequence\n        innerOptions.path = (options.path ? `${options.path}.` : '') + prop;\n        field = field.resolve({\n          value: inputValue,\n          context: options.context,\n          parent: intermediateValue\n        });\n        let fieldSpec = field instanceof Schema ? field.spec : undefined;\n        let strict = fieldSpec == null ? void 0 : fieldSpec.strict;\n        if (fieldSpec != null && fieldSpec.strip) {\n          isChanged = isChanged || prop in value;\n          continue;\n        }\n        fieldValue = !options.__validating || !strict ?\n        // TODO: use _cast, this is double resolving\n        field.cast(value[prop], innerOptions) : value[prop];\n        if (fieldValue !== undefined) {\n          intermediateValue[prop] = fieldValue;\n        }\n      } else if (exists && !strip) {\n        intermediateValue[prop] = value[prop];\n      }\n      if (exists !== prop in intermediateValue || intermediateValue[prop] !== value[prop]) {\n        isChanged = true;\n      }\n    }\n    return isChanged ? intermediateValue : value;\n  }\n  _validate(_value, options = {}, panic, next) {\n    let {\n      from = [],\n      originalValue = _value,\n      recursive = this.spec.recursive\n    } = options;\n    options.from = [{\n      schema: this,\n      value: originalValue\n    }, ...from];\n    // this flag is needed for handling `strict` correctly in the context of\n    // validation vs just casting. e.g strict() on a field is only used when validating\n    options.__validating = true;\n    options.originalValue = originalValue;\n    super._validate(_value, options, panic, (objectErrors, value) => {\n      if (!recursive || !isObject(value)) {\n        next(objectErrors, value);\n        return;\n      }\n      originalValue = originalValue || value;\n      let tests = [];\n      for (let key of this._nodes) {\n        let field = this.fields[key];\n        if (!field || Reference.isRef(field)) {\n          continue;\n        }\n        tests.push(field.asNestedTest({\n          options,\n          key,\n          parent: value,\n          parentPath: options.path,\n          originalParent: originalValue\n        }));\n      }\n      this.runTests({\n        tests,\n        value,\n        originalValue,\n        options\n      }, panic, fieldErrors => {\n        next(fieldErrors.sort(this._sortErrors).concat(objectErrors), value);\n      });\n    });\n  }\n  clone(spec) {\n    const next = super.clone(spec);\n    next.fields = Object.assign({}, this.fields);\n    next._nodes = this._nodes;\n    next._excludedEdges = this._excludedEdges;\n    next._sortErrors = this._sortErrors;\n    return next;\n  }\n  concat(schema) {\n    let next = super.concat(schema);\n    let nextFields = next.fields;\n    for (let [field, schemaOrRef] of Object.entries(this.fields)) {\n      const target = nextFields[field];\n      nextFields[field] = target === undefined ? schemaOrRef : target;\n    }\n    return next.withMutation(s =>\n    // XXX: excludes here is wrong\n    s.setFields(nextFields, [...this._excludedEdges, ...schema._excludedEdges]));\n  }\n  _getDefault(options) {\n    if ('default' in this.spec) {\n      return super._getDefault(options);\n    }\n\n    // if there is no default set invent one\n    if (!this._nodes.length) {\n      return undefined;\n    }\n    let dft = {};\n    this._nodes.forEach(key => {\n      var _innerOptions;\n      const field = this.fields[key];\n      let innerOptions = options;\n      if ((_innerOptions = innerOptions) != null && _innerOptions.value) {\n        innerOptions = Object.assign({}, innerOptions, {\n          parent: innerOptions.value,\n          value: innerOptions.value[key]\n        });\n      }\n      dft[key] = field && 'getDefault' in field ? field.getDefault(innerOptions) : undefined;\n    });\n    return dft;\n  }\n  setFields(shape, excludedEdges) {\n    let next = this.clone();\n    next.fields = shape;\n    next._nodes = sortFields(shape, excludedEdges);\n    next._sortErrors = sortByKeyOrder(Object.keys(shape));\n    // XXX: this carries over edges which may not be what you want\n    if (excludedEdges) next._excludedEdges = excludedEdges;\n    return next;\n  }\n  shape(additions, excludes = []) {\n    return this.clone().withMutation(next => {\n      let edges = next._excludedEdges;\n      if (excludes.length) {\n        if (!Array.isArray(excludes[0])) excludes = [excludes];\n        edges = [...next._excludedEdges, ...excludes];\n      }\n\n      // XXX: excludes here is wrong\n      return next.setFields(Object.assign(next.fields, additions), edges);\n    });\n  }\n  partial() {\n    const partial = {};\n    for (const [key, schema] of Object.entries(this.fields)) {\n      partial[key] = 'optional' in schema && schema.optional instanceof Function ? schema.optional() : schema;\n    }\n    return this.setFields(partial);\n  }\n  deepPartial() {\n    const next = deepPartial(this);\n    return next;\n  }\n  pick(keys) {\n    const picked = {};\n    for (const key of keys) {\n      if (this.fields[key]) picked[key] = this.fields[key];\n    }\n    return this.setFields(picked, this._excludedEdges.filter(([a, b]) => keys.includes(a) && keys.includes(b)));\n  }\n  omit(keys) {\n    const remaining = [];\n    for (const key of Object.keys(this.fields)) {\n      if (keys.includes(key)) continue;\n      remaining.push(key);\n    }\n    return this.pick(remaining);\n  }\n  from(from, to, alias) {\n    let fromGetter = (0,property_expr__WEBPACK_IMPORTED_MODULE_0__.getter)(from, true);\n    return this.transform(obj => {\n      if (!obj) return obj;\n      let newObj = obj;\n      if (deepHas(obj, from)) {\n        newObj = Object.assign({}, obj);\n        if (!alias) delete newObj[from];\n        newObj[to] = fromGetter(obj);\n      }\n      return newObj;\n    });\n  }\n\n  /** Parse an input JSON string to an object */\n  json() {\n    return this.transform(parseJson);\n  }\n\n  /**\n   * Similar to `noUnknown` but only validates that an object is the right shape without stripping the unknown keys\n   */\n  exact(message) {\n    return this.test({\n      name: 'exact',\n      exclusive: true,\n      message: message || object.exact,\n      test(value) {\n        if (value == null) return true;\n        const unknownKeys = unknown(this.schema, value);\n        return unknownKeys.length === 0 || this.createError({\n          params: {\n            properties: unknownKeys.join(', ')\n          }\n        });\n      }\n    });\n  }\n  stripUnknown() {\n    return this.clone({\n      noUnknown: true\n    });\n  }\n  noUnknown(noAllow = true, message = object.noUnknown) {\n    if (typeof noAllow !== 'boolean') {\n      message = noAllow;\n      noAllow = true;\n    }\n    let next = this.test({\n      name: 'noUnknown',\n      exclusive: true,\n      message: message,\n      test(value) {\n        if (value == null) return true;\n        const unknownKeys = unknown(this.schema, value);\n        return !noAllow || unknownKeys.length === 0 || this.createError({\n          params: {\n            unknown: unknownKeys.join(', ')\n          }\n        });\n      }\n    });\n    next.spec.noUnknown = noAllow;\n    return next;\n  }\n  unknown(allow = true, message = object.noUnknown) {\n    return this.noUnknown(!allow, message);\n  }\n  transformKeys(fn) {\n    return this.transform(obj => {\n      if (!obj) return obj;\n      const result = {};\n      for (const key of Object.keys(obj)) result[fn(key)] = obj[key];\n      return result;\n    });\n  }\n  camelCase() {\n    return this.transformKeys(tiny_case__WEBPACK_IMPORTED_MODULE_1__.camelCase);\n  }\n  snakeCase() {\n    return this.transformKeys(tiny_case__WEBPACK_IMPORTED_MODULE_1__.snakeCase);\n  }\n  constantCase() {\n    return this.transformKeys(key => (0,tiny_case__WEBPACK_IMPORTED_MODULE_1__.snakeCase)(key).toUpperCase());\n  }\n  describe(options) {\n    const next = (options ? this.resolve(options) : this).clone();\n    const base = super.describe(options);\n    base.fields = {};\n    for (const [key, value] of Object.entries(next.fields)) {\n      var _innerOptions2;\n      let innerOptions = options;\n      if ((_innerOptions2 = innerOptions) != null && _innerOptions2.value) {\n        innerOptions = Object.assign({}, innerOptions, {\n          parent: innerOptions.value,\n          value: innerOptions.value[key]\n        });\n      }\n      base.fields[key] = value.describe(innerOptions);\n    }\n    return base;\n  }\n}\ncreate$3.prototype = ObjectSchema.prototype;\n\nfunction create$2(type) {\n  return new ArraySchema(type);\n}\nclass ArraySchema extends Schema {\n  constructor(type) {\n    super({\n      type: 'array',\n      spec: {\n        types: type\n      },\n      check(v) {\n        return Array.isArray(v);\n      }\n    });\n\n    // `undefined` specifically means uninitialized, as opposed to \"no subtype\"\n    this.innerType = void 0;\n    this.innerType = type;\n  }\n  _cast(_value, _opts) {\n    const value = super._cast(_value, _opts);\n\n    // should ignore nulls here\n    if (!this._typeCheck(value) || !this.innerType) {\n      return value;\n    }\n    let isChanged = false;\n    const castArray = value.map((v, idx) => {\n      const castElement = this.innerType.cast(v, Object.assign({}, _opts, {\n        path: `${_opts.path || ''}[${idx}]`\n      }));\n      if (castElement !== v) {\n        isChanged = true;\n      }\n      return castElement;\n    });\n    return isChanged ? castArray : value;\n  }\n  _validate(_value, options = {}, panic, next) {\n    var _options$recursive;\n    // let sync = options.sync;\n    // let path = options.path;\n    let innerType = this.innerType;\n    // let endEarly = options.abortEarly ?? this.spec.abortEarly;\n    let recursive = (_options$recursive = options.recursive) != null ? _options$recursive : this.spec.recursive;\n    options.originalValue != null ? options.originalValue : _value;\n    super._validate(_value, options, panic, (arrayErrors, value) => {\n      var _options$originalValu2;\n      if (!recursive || !innerType || !this._typeCheck(value)) {\n        next(arrayErrors, value);\n        return;\n      }\n\n      // #950 Ensure that sparse array empty slots are validated\n      let tests = new Array(value.length);\n      for (let index = 0; index < value.length; index++) {\n        var _options$originalValu;\n        tests[index] = innerType.asNestedTest({\n          options,\n          index,\n          parent: value,\n          parentPath: options.path,\n          originalParent: (_options$originalValu = options.originalValue) != null ? _options$originalValu : _value\n        });\n      }\n      this.runTests({\n        value,\n        tests,\n        originalValue: (_options$originalValu2 = options.originalValue) != null ? _options$originalValu2 : _value,\n        options\n      }, panic, innerTypeErrors => next(innerTypeErrors.concat(arrayErrors), value));\n    });\n  }\n  clone(spec) {\n    const next = super.clone(spec);\n    // @ts-expect-error readonly\n    next.innerType = this.innerType;\n    return next;\n  }\n\n  /** Parse an input JSON string to an object */\n  json() {\n    return this.transform(parseJson);\n  }\n  concat(schema) {\n    let next = super.concat(schema);\n\n    // @ts-expect-error readonly\n    next.innerType = this.innerType;\n    if (schema.innerType)\n      // @ts-expect-error readonly\n      next.innerType = next.innerType ?\n      // @ts-expect-error Lazy doesn't have concat and will break\n      next.innerType.concat(schema.innerType) : schema.innerType;\n    return next;\n  }\n  of(schema) {\n    // FIXME: this should return a new instance of array without the default to be\n    let next = this.clone();\n    if (!isSchema(schema)) throw new TypeError('`array.of()` sub-schema must be a valid yup schema not: ' + printValue(schema));\n\n    // @ts-expect-error readonly\n    next.innerType = schema;\n    next.spec = Object.assign({}, next.spec, {\n      types: schema\n    });\n    return next;\n  }\n  length(length, message = array.length) {\n    return this.test({\n      message,\n      name: 'length',\n      exclusive: true,\n      params: {\n        length\n      },\n      skipAbsent: true,\n      test(value) {\n        return value.length === this.resolve(length);\n      }\n    });\n  }\n  min(min, message) {\n    message = message || array.min;\n    return this.test({\n      message,\n      name: 'min',\n      exclusive: true,\n      params: {\n        min\n      },\n      skipAbsent: true,\n      // FIXME(ts): Array<typeof T>\n      test(value) {\n        return value.length >= this.resolve(min);\n      }\n    });\n  }\n  max(max, message) {\n    message = message || array.max;\n    return this.test({\n      message,\n      name: 'max',\n      exclusive: true,\n      params: {\n        max\n      },\n      skipAbsent: true,\n      test(value) {\n        return value.length <= this.resolve(max);\n      }\n    });\n  }\n  ensure() {\n    return this.default(() => []).transform((val, original) => {\n      // We don't want to return `null` for nullable schema\n      if (this._typeCheck(val)) return val;\n      return original == null ? [] : [].concat(original);\n    });\n  }\n  compact(rejector) {\n    let reject = !rejector ? v => !!v : (v, i, a) => !rejector(v, i, a);\n    return this.transform(values => values != null ? values.filter(reject) : values);\n  }\n  describe(options) {\n    const next = (options ? this.resolve(options) : this).clone();\n    const base = super.describe(options);\n    if (next.innerType) {\n      var _innerOptions;\n      let innerOptions = options;\n      if ((_innerOptions = innerOptions) != null && _innerOptions.value) {\n        innerOptions = Object.assign({}, innerOptions, {\n          parent: innerOptions.value,\n          value: innerOptions.value[0]\n        });\n      }\n      base.innerType = next.innerType.describe(innerOptions);\n    }\n    return base;\n  }\n}\ncreate$2.prototype = ArraySchema.prototype;\n\n// @ts-ignore\nfunction create$1(schemas) {\n  return new TupleSchema(schemas);\n}\nclass TupleSchema extends Schema {\n  constructor(schemas) {\n    super({\n      type: 'tuple',\n      spec: {\n        types: schemas\n      },\n      check(v) {\n        const types = this.spec.types;\n        return Array.isArray(v) && v.length === types.length;\n      }\n    });\n    this.withMutation(() => {\n      this.typeError(tuple.notType);\n    });\n  }\n  _cast(inputValue, options) {\n    const {\n      types\n    } = this.spec;\n    const value = super._cast(inputValue, options);\n    if (!this._typeCheck(value)) {\n      return value;\n    }\n    let isChanged = false;\n    const castArray = types.map((type, idx) => {\n      const castElement = type.cast(value[idx], Object.assign({}, options, {\n        path: `${options.path || ''}[${idx}]`\n      }));\n      if (castElement !== value[idx]) isChanged = true;\n      return castElement;\n    });\n    return isChanged ? castArray : value;\n  }\n  _validate(_value, options = {}, panic, next) {\n    let itemTypes = this.spec.types;\n    super._validate(_value, options, panic, (tupleErrors, value) => {\n      var _options$originalValu2;\n      // intentionally not respecting recursive\n      if (!this._typeCheck(value)) {\n        next(tupleErrors, value);\n        return;\n      }\n      let tests = [];\n      for (let [index, itemSchema] of itemTypes.entries()) {\n        var _options$originalValu;\n        tests[index] = itemSchema.asNestedTest({\n          options,\n          index,\n          parent: value,\n          parentPath: options.path,\n          originalParent: (_options$originalValu = options.originalValue) != null ? _options$originalValu : _value\n        });\n      }\n      this.runTests({\n        value,\n        tests,\n        originalValue: (_options$originalValu2 = options.originalValue) != null ? _options$originalValu2 : _value,\n        options\n      }, panic, innerTypeErrors => next(innerTypeErrors.concat(tupleErrors), value));\n    });\n  }\n  describe(options) {\n    const next = (options ? this.resolve(options) : this).clone();\n    const base = super.describe(options);\n    base.innerType = next.spec.types.map((schema, index) => {\n      var _innerOptions;\n      let innerOptions = options;\n      if ((_innerOptions = innerOptions) != null && _innerOptions.value) {\n        innerOptions = Object.assign({}, innerOptions, {\n          parent: innerOptions.value,\n          value: innerOptions.value[index]\n        });\n      }\n      return schema.describe(innerOptions);\n    });\n    return base;\n  }\n}\ncreate$1.prototype = TupleSchema.prototype;\n\nfunction create(builder) {\n  return new Lazy(builder);\n}\nfunction catchValidationError(fn) {\n  try {\n    return fn();\n  } catch (err) {\n    if (ValidationError.isError(err)) return Promise.reject(err);\n    throw err;\n  }\n}\nclass Lazy {\n  constructor(builder) {\n    this.type = 'lazy';\n    this.__isYupSchema__ = true;\n    this.spec = void 0;\n    this._resolve = (value, options = {}) => {\n      let schema = this.builder(value, options);\n      if (!isSchema(schema)) throw new TypeError('lazy() functions must return a valid schema');\n      if (this.spec.optional) schema = schema.optional();\n      return schema.resolve(options);\n    };\n    this.builder = builder;\n    this.spec = {\n      meta: undefined,\n      optional: false\n    };\n  }\n  clone(spec) {\n    const next = new Lazy(this.builder);\n    next.spec = Object.assign({}, this.spec, spec);\n    return next;\n  }\n  optionality(optional) {\n    const next = this.clone({\n      optional\n    });\n    return next;\n  }\n  optional() {\n    return this.optionality(true);\n  }\n  resolve(options) {\n    return this._resolve(options.value, options);\n  }\n  cast(value, options) {\n    return this._resolve(value, options).cast(value, options);\n  }\n  asNestedTest(config) {\n    let {\n      key,\n      index,\n      parent,\n      options\n    } = config;\n    let value = parent[index != null ? index : key];\n    return this._resolve(value, Object.assign({}, options, {\n      value,\n      parent\n    })).asNestedTest(config);\n  }\n  validate(value, options) {\n    return catchValidationError(() => this._resolve(value, options).validate(value, options));\n  }\n  validateSync(value, options) {\n    return this._resolve(value, options).validateSync(value, options);\n  }\n  validateAt(path, value, options) {\n    return catchValidationError(() => this._resolve(value, options).validateAt(path, value, options));\n  }\n  validateSyncAt(path, value, options) {\n    return this._resolve(value, options).validateSyncAt(path, value, options);\n  }\n  isValid(value, options) {\n    try {\n      return this._resolve(value, options).isValid(value, options);\n    } catch (err) {\n      if (ValidationError.isError(err)) {\n        return Promise.resolve(false);\n      }\n      throw err;\n    }\n  }\n  isValidSync(value, options) {\n    return this._resolve(value, options).isValidSync(value, options);\n  }\n  describe(options) {\n    return options ? this.resolve(options).describe(options) : {\n      type: 'lazy',\n      meta: this.spec.meta,\n      label: undefined\n    };\n  }\n  meta(...args) {\n    if (args.length === 0) return this.spec.meta;\n    let next = this.clone();\n    next.spec.meta = Object.assign(next.spec.meta || {}, args[0]);\n    return next;\n  }\n  get ['~standard']() {\n    const schema = this;\n    const standard = {\n      version: 1,\n      vendor: 'yup',\n      async validate(value) {\n        try {\n          const result = await schema.validate(value, {\n            abortEarly: false\n          });\n          return {\n            value: result\n          };\n        } catch (err) {\n          if (ValidationError.isError(err)) {\n            return {\n              issues: issuesFromValidationError(err)\n            };\n          }\n          throw err;\n        }\n      }\n    };\n    return standard;\n  }\n}\n\nfunction setLocale(custom) {\n  Object.keys(custom).forEach(type => {\n    // @ts-ignore\n    Object.keys(custom[type]).forEach(method => {\n      // @ts-ignore\n      locale[type][method] = custom[type][method];\n    });\n  });\n}\n\nfunction addMethod(schemaType, name, fn) {\n  if (!schemaType || !isSchema(schemaType.prototype)) throw new TypeError('You must provide a yup schema constructor function');\n  if (typeof name !== 'string') throw new TypeError('A Method name must be provided');\n  if (typeof fn !== 'function') throw new TypeError('Method function must be provided');\n  schemaType.prototype[name] = fn;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/yup/index.esm.js\n");

/***/ })

};
;