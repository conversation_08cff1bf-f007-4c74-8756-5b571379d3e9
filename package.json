{"name": "menu-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "tsc --noEmit"}, "dependencies": {"@hookform/resolvers": "^3.9.1", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-slot": "^1.1.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.468.0", "next": "15.4.6", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.53.2", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "yup": "^1.4.0"}, "devDependencies": {"@types/node": "^22.10.2", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@typescript-eslint/eslint-plugin": "^8.18.2", "@typescript-eslint/parser": "^8.18.2", "autoprefixer": "^10.4.20", "eslint": "^9.17.0", "eslint-config-next": "15.4.6", "postcss": "^8.5.1", "prettier": "^3.4.2", "prettier-plugin-tailwindcss": "^0.6.9", "tailwindcss": "^3.4.17", "typescript": "^5.7.2"}, "engines": {"node": ">=18.17.0"}}